# 新型抗革兰氏阴性菌肽段生成模型设计方案

## 1. 引言与项目目标

### 1.1 项目背景
抗生素耐药性是全球公共卫生的重大威胁。革兰氏阴性菌由于其独特的细胞壁结构，往往对现有抗生素具有更强的耐药性，导致感染治疗困难。抗菌肽（Antimicrobial Peptides, AMPs）作为一类具有广谱抗菌活性、不易产生耐药性的天然免疫分子，为对抗耐药菌提供了新的希望。本项目旨在利用先进的深度学习技术，设计和生成针对革兰氏阴性菌（AGN）的新型、高效抗菌肽。

### 1.2 核心目标
本项目的核心目标是开发一个深度学习模型，该模型能够：
1.  有效学习和整合肽段的**氨基酸序列信息**和其对应的**三维空间结构信息**。
2.  基于学习到的高质量序列-结构联合表示，**生成具有抗革兰氏阴性菌（AGN）潜力的新型肽段序列及其预测结构**。

### 1.3 总体思路
为实现上述目标，我们提出一个分阶段的建模策略：

1.  **第一阶段：多模态对比表示学习**
    *   利用已有的AGN肽段及其AlphaFold预测结构，训练一个双编码器模型（序列编码器 + 结构编码器）。
    *   通过多模态对比学习（InfoNCE损失）以及辅助的自监督任务（如MLM、二级结构预测），使模型学习到能够捕捉序列与结构内在关联的联合表示 `z`。
2.  **第二阶段：条件化生成**
    *   采用“**结构优先，序列着色**”的生成策略。
    *   首先，训练一个**条件结构扩散模型**，以第一阶段学习到的联合表示 `z` 为条件，生成新的蛋白质Cα骨架。
    *   然后，训练一个**条件序列设计模型**，为生成的Cα骨架“着色”上氨基酸序列，同样以联合表示 `z` 和生成的骨架为条件。

## 2. 数据准备与预处理

### 2.1 数据集来源与构成
您提供了以下宝贵的数据资源：

*   **P1: 纯抗革兰氏阴性菌 (AGN) 数据：** 764条肽段序列，均有对应的AlphaFold预测结构。
    *   **用途：** 核心正样本。
*   **P2: 同时抗革兰氏阴性菌和阳性菌 (AGN&AGP) 数据：** 1800余条肽段序列，均有对应的AlphaFold预测结构。
    *   **用途：** 核心正样本（因为它们也具备AGN活性）。
*   **N1: 纯抗革兰氏阳性菌 (AGP) 数据：** 800条肽段序列，均有对应的AlphaFold预测结构。
    *   **用途：** 强区分性负样本。
*   **N2: 大型AMP数据库：** 包含约3600余条序列（已包含P1, P2, N1）。您拥有一个准确率超过90%的AGN分类器。
    *   **用途：** 从中筛选“其他非AGN AMP”作为通用/背景负样本。需要为筛选出的序列生成AlphaFold结构。

### 2.2 结构数据处理
*   **AlphaFold预测：** 对于N2中筛选出的非AGN AMP序列，需要使用AlphaFold (或类似工具) 预测其3D结构，并保存为 `.cif` 或 `.pdb` 文件。
*   **Cα坐标提取：** 对于所有结构数据，提取每个残基的Cα原子三维坐标 (`x, y, z`)。这将作为结构编码器（如GVP）的主要输入。

### 2.3 二级结构标签生成 (用于辅助损失)
*   使用**DSSP (Define Secondary Structure of Proteins)** 算法或其实现（如Biopython库中的`Bio.PDB.DSSP`模块）处理每个 `.cif` / `.pdb` 文件。
*   为序列中的每个残基分配一个二级结构标签。建议简化为3类：
    *   **H:** α-helix (DSSP中的 'H')
    *   **E:** β-strand (DSSP中的 'E')
    *   **C:** Coil/Loop (DSSP中所有其他状态，如 'T', 'S', 'G', 'B', 'I', ' ')
*   确保生成的二级结构标签序列与氨基酸序列严格对齐。

### 2.4 数据集划分
*   将整合后的数据集（包含所有正样本和已准备好结构信息的负样本）划分为训练集、验证集和（可选的）测试集。
*   **推荐比例：** 训练集80%，验证集10%-15%，测试集5%-10%。
*   **划分策略：** 随机划分，但要确保划分时保持“序列-结构”对的完整性。如果数据来源多样，可以考虑分层采样以保证各类别在不同集合中的分布大致相似。
*   验证集用于模型选择、超参数调优和早停。测试集用于最终模型的性能评估（应在所有开发完成后使用一次）。

## 3. 第一阶段：多模态对比表示学习框架

### 3.1 目标
学习一个高质量的、能够融合肽段序列信息和三维结构信息的联合表示向量 `z`。这个 `z` 应能有效区分具有AGN活性的肽段与其他肽段。

### 3.2 模型架构

#### 3.2.1 序列编码器 (`f_seq`)
*   **架构：** 基于预训练的 **ESM-2 模型**。
*   **推荐型号：**
    *   **ESM-2 150M (esm2_t30_150M_UR50D)：** 30层Transformer，640维隐藏状态，20个注意力头。作为起点，在性能和效率间有较好平衡。
    *   **ESM-2 650M (esm2_t33_650M_UR50D)：** 33层Transformer，1280维隐藏状态，20个注意力头。表示能力更强，如果计算资源允许，可以尝试。
*   **输入：** 氨基酸序列 (tokenized)。
*   **输出：** 每个残基的上下文相关特征向量 (例如，维度640或1280)。可以取`[CLS]` token的输出作为整个序列的表示，或对所有残基的输出进行平均池化。对于后续与残基级GVP的交互，每个残基的特征更常用。
*   **微调策略：** 加载预训练权重，进行全模型微调，但对ESM-2的预训练层使用较小的学习率。

#### 3.2.2 结构编码器 (`f_struct`)
*   **架构：** **残基级GVP (Geometric Vector Perceptron) 网络**。每个Cα原子视为一个图节点。
*   **输入节点特征 (每个Cα节点 `i`)：**
    *   **标量特征 `s_i` (例如，总维度约256D)：**
        *   氨基酸类型嵌入：20种氨基酸one-hot编码后，通过嵌入层映射到64维。
        *   来自`f_seq`的序列特征：对应残基的ESM-2输出特征（如640D）通过线性层投影到192维。
    *   **向量特征 `v_i` (例如，8个向量通道)：**
        *   初始化为 `8x3` 的零矩阵。模型将学习填充这些通道。
*   **输入边特征 (连接Cα节点 `i` 和 `j`)：**
    *   **边的构建：** k-近邻 (k-NN)，例如 `k=20`。
    *   **标量边特征 `e_s_ij` (例如，16D)：** Cα_i和Cα_j间距离的RBF扩展。
    *   **向量边特征 `e_v_ij` (1个向量通道)：** 归一化的相对位置向量 `(pos_j - pos_i) / ||pos_j - pos_i||` (1x3)。
*   **GVP卷积层 (GVPConv)：**
    *   **层数：** 4-5层。
    *   **内部标量维度：** 256维。
    *   **内部向量通道数：** 16个。
*   **图读出/池化：**
    *   对最后一层GVP输出的节点标量特征进行全局平均池化。
    *   对最后一层GVP输出的节点向量特征的模长进行全局平均池化。
    *   将两者拼接，然后通过一个MLP（例如 `Linear(256+16, 512) -> ReLU -> Linear(512, 512)`）映射到最终的图级别结构嵌入，例如**512维**。

#### 3.2.3 投影头 (Projection Heads)
*   **目的：** 将序列编码器和结构编码器输出的（可能不同维度的）特征映射到同一个低维嵌入空间，用于计算对比损失。
*   **架构：** 两个独立的MLP，一个用于序列特征，一个用于结构特征。
    *   例如：`Linear(D_encoder_output) -> ReLU -> Linear(D_embedding)`
*   **输出嵌入维度 `D_embedding`：** 推荐 **256维**。
    *   序列投影头输入：`f_seq` 的输出（如640D或池化后的维度）。
    *   结构投影头输入：`f_struct` 的输出（如512D）。

### 3.3 数据与采样策略

#### 3.3.1 正样本对
*   对于每个来自P1 (纯AGN) 或 P2 (AGN&AGP) 的肽段 `(s_orig, x_orig)`：
    *   应用数据增强得到 `(s_aug, x_aug)`。
    *   其序列特征 `f_seq(s_aug)` 和结构特征 `f_struct(x_aug)` （经过各自投影头后）构成一个正样本对。

#### 3.3.2 负样本对
对于一个锚点（例如，`f_seq(s_aug)` 作为查询 `q`）：
*   **批内负样本 (In-batch negatives)：** 当前训练批次中其他样本的（投影后的）结构特征。
*   **Memory Bank 负样本：**
    *   **组成：** 存储来自N1 (纯AGP) 和N2筛选出的其他非AGN AMP的（投影后的）结构键特征。
    *   **大小 `K_bank`：** 约等于总的独特负样本数量 (例如2000-3000)。
    *   **更新机制：** 采用MoCo (Momentum Contrast) 风格。
        *   维护一个独立的、动量更新的**键编码器** (`f_k_seq`, `f_k_struct` 和对应的投影头)。其参数 `θ_k` 是查询编码器参数 `θ_q` 的动量平均：`θ_k ← m * θ_k + (1-m) * θ_q` (动量系数 `m` 通常接近1，如0.999)。
        *   Bank中存储的是由键编码器产生的特征。当新的负样本（来自当前批次或外部采样）被键编码器处理后，其特征可以用来更新Bank中对应样本的条目（如果Bank大小等于总负样本数）或以FIFO方式替换旧条目（如果Bank是固定大小队列）。鉴于我们的负样本总量，前者更合适。
    *   **采样：** 在计算损失时，从Memory Bank中随机采样一部分负样本键参与对比。

#### 3.3.3 数据增强 (在线/动态进行)
*   **序列增强：** 对输入序列进行小比例（例如5%的残基）的随机氨基酸替换（可优先考虑BLOSUM62相似替换）。
*   **结构增强：** 对输入的Cα坐标添加微小的高斯噪声（例如，标准差0.2Å - 0.3Å）。

### 3.4 损失函数设计

#### 3.4.1 主要损失：双向InfoNCE对比损失 (`L_contrastive`)
对于一个查询 `q` (来自一个模态，如序列)，其对应的正样本键 `k+` (来自另一模态，如结构)，以及一组负样本键 `{k_i}` (来自另一模态的非配对样本)：
`L_q = -log [ exp(sim(q, k+) / τ) / ( exp(sim(q, k+) / τ) + Σ_i exp(sim(q, k_i) / τ) ) ]`
*   `sim(u, v)`：余弦相似度，`u` 和 `v` 是L2归一化的投影后嵌入。
*   `τ` (温度)：超参数，例如 **0.1** (需调优)。

总对比损失为：
`L_contrastive = L_seq_to_struct + L_struct_to_seq`
*   `L_seq_to_struct`: 以投影后的序列特征为查询 `q_s`，投影后的配对结构特征为 `k_x+`，其他结构特征为 `{k_x,i}`。
*   `L_struct_to_seq`: 以投影后的结构特征为查询 `q_x`，投影后的配对序列特征为 `k_s+`，其他序列特征为 `{k_s,i}`。

#### 3.4.2 辅助损失
1.  **序列Masked Language Modeling损失 (`L_mlm`)：**
    *   对输入序列按BERT/ESM策略进行15%的masking。
    *   在序列编码器 `f_seq` 之上添加一个MLM预测头（线性层输出到词汇表大小）。
    *   对被mask位置计算交叉熵损失。
2.  **二级结构预测损失 (`L_ss`)：**
    *   利用序列编码器 `f_seq` 输出的每个残基的特征。
    *   添加一个SS预测头（线性层输出到二级结构类别数，如3类H,E,C）。
    *   对每个残基计算交叉熵损失。

#### 3.4.3 总损失
`L_total = L_contrastive + λ_mlm * L_mlm + λ_ss * L_ss`
*   `λ_mlm`, `λ_ss`：权重超参数，平衡各项损失的贡献 (例如，初始可设为1.0，然后根据各项损失的量级和收敛情况调整)。

### 3.5 训练细节
*   **优化器：** AdamW (例如，`betas=(0.9, 0.999)`, `eps=1e-8`)。
*   **学习率 (LR) 与调度：**
    *   基础学习率：例如 `5e-5` (对于ESM-2部分) 或 `1e-4` (对于新加模块)。可考虑差分学习率。
    *   调度器：Warmup (例如总训练步数的10%) + Cosine Annealing Decay (衰减到0或一个非常小的值)。
*   **批次大小 (Batch Size)：**
    *   根据2xA100 80GB显存，每GPU可尝试32, 64, 或128。总批次大小为 `batch_size_per_gpu * num_gpus`。
    *   例如，每GPU 64，总批次128。
*   **训练周期 (Epochs)：** 50-100 epochs，或根据早停决定。
*   **早停 (Early Stopping)：**
    *   监控验证集上的 `val_contrastive_loss`。
    *   `patience`: 10-15个评估周期。
    *   `min_delta`: 例如0.0001。
    *   `restore_best_weights=True`。
*   **梯度裁剪 (Gradient Clipping)：** 可选，例如裁剪全局范数到1.0。
*   **混合精度训练 (Mixed Precision)：** 强烈建议启用 (如PyTorch `torch.cuda.amp`) 以加速并减少显存。
*   **分布式训练 (Distributed Training)：** 使用PyTorch DDP。

### 3.6 权重初始化
*   **序列编码器 (`f_seq`)：** 加载ESM-2预训练权重。
*   **结构编码器 (`f_struct`, GVP) 及所有新添加的MLP头 (投影头, MLM头, SS头)：**
    *   线性层权重：Kaiming (He) Normal/Uniform 初始化 (若后续激活为ReLU类)。对于直接输出分类的最后一层，Xavier Normal/Uniform也可考虑。
    *   偏置项：初始化为零。

## 4. 第二阶段：条件生成模块 (“结构优先，序列着色”)

### 4.1 目标
以第一阶段学习到的高质量联合表示 `z` 为条件，生成具有AGN潜力的新型肽段序列及其对应的三维结构。

### 4.2 模块一：条件结构扩散网络 (Cα骨架生成)
*   **架构：** E(3)-等变去噪U-Net。
    *   骨干：U-Net结构，卷积层替换为GVPConv, EGNNConv或SE(3)-Transformer层。
    *   输入：噪声化的Cα坐标 `coords_t` (Lx3)，时间步 `t` 嵌入，条件 `z` 嵌入。
    *   条件 `z` 融入：作为全局条件或通过FiLM层等方式加入到网络各层。
    *   输出：预测的噪声 `ε_coords` (Lx3)。
*   **训练损失 (`L_struct_diffusion`)：**
    *   `L_struct_diffusion = || ε_true - model_θ_struct(coords_t, t, z) ||^2` (MSE)。
    *   可考虑FAPE损失作为进阶。
*   **训练数据：** 真实的Cα骨架 `coords_0` 及其对应的 `z` (由固定的预训练对比学习编码器提取)。
*   **采样：** 从高斯噪声 `coords_T` 开始，以目标 `z_target` 为条件，使用DDPM或DDIM等算法迭代去噪，生成 `coords_0_generated`。

### 4.3 模块二：条件序列设计模型 (为骨架“着色”)
*   **架构 (借鉴ProteinMPNN)：**
    *   **骨架编码器：** E(3)-等变GNN (如GVPConv) 处理输入的Cα骨架 `coords_0_generated` (或训练时的`coords_0_real`)，并融合条件 `z`，输出每个Cα位置的结构特征 `h_node_i`。
    *   **氨基酸预测头：** 对每个 `h_node_i` (可再次融合`z`) 使用线性层（或小型MLP）预测氨基酸类型的logits。初期采用逐位独立预测。
*   **训练损失 (`L_seq_design`)：**
    *   `L_seq_design = Σ_positions CrossEntropy(softmax(logits_aa_position), true_aa_position)`
*   **训练数据：** 真实的Cα骨架 `coords_0_real`，对应的真实氨基酸序列 `seq_real`，以及对应的 `z`。
*   **采样：** 输入生成的骨架 `coords_0_generated` 和目标 `z_target`，通过argmax或随机采样得到氨基酸序列。

### 4.4 整体训练策略 (生成模块)
*   **推荐分阶段训练：**
    1.  (已完成) 训练好多模态对比学习编码器，得到固定的 `z` 提取器。
    2.  固定编码器，独立训练条件结构扩散模型。
    3.  固定编码器和结构扩散模型（或使用其生成的骨架样本），独立训练条件序列设计模型。
*   **后续优化：** 可考虑联合微调或更复杂的交替训练方案。

## 5. 模型评估策略

### 5.1 表示学习阶段评估 (验证集)
*   **主要指标：**
    *   `val_contrastive_loss` (InfoNCE损失)。
    *   检索准确率：`val_retrieval_TopK_accuracy` (例如K=1, 5, 10)，衡量给定序列能否找回正确结构，反之亦然。
*   **辅助指标：**
    *   `val_mlm_accuracy` / `val_mlm_perplexity`。
    *   `val_ss_accuracy` / `val_ss_F1_score` (如果类别不平衡)。
*   **可视化：** 使用t-SNE或UMAP对学习到的联合表示 `z` 进行降维可视化，观察不同类别样本（AGN, AGP, 其他）的分布和区分情况。

### 5.2 生成阶段评估

#### 5.2.1 生成序列的评估
*   **物理化学性质分布：** 比较生成序列的长度、净电荷、疏水性、等电点等分布是否与真实的AGN肽段相似。
*   **序列新颖性 (Novelty)：** 与训练集中已知序列的相似度（如BLAST比对，编辑距离）。我们期望生成与已知序列不同但仍有效的新序列。
*   **序列多样性 (Diversity)：** 生成序列集合内部的相互相似度。
*   **可折叠性/结构合理性预测：** 使用AlphaFold等工具预测生成序列的结构，评估其pLDDT分数，以及是否能形成明确的二级/三级结构。
*   **预测AGN活性：** 使用外部的、独立的AGN活性预测器（如果可用，包括您自己的分类器）评估生成序列的潜在活性。

#### 5.2.2 生成结构的评估 (Cα骨架)
*   **与已知AGN结构的相似性/差异性：** 例如，计算RMSD到最近的已知AGN结构骨架（如果适用，但这可能限制新颖性）。
*   **结构合理性指标：**
    *   Cα-Cα距离分布是否符合典型值。
    *   回旋半径。
    *   （如果生成了全原子骨架）键长、键角、二面角分布，Ramachandran图。
*   **与条件 `z` 的一致性：** 这是一个较难直接量化的指标。可以通过将生成的 `(seq_generated, struct_generated)` 对重新送入对比学习的编码器，看其产生的 `z_generated` 与输入的条件 `z_target` 的相似度。

#### 5.2.3 最终评估
*   **实验验证：** 这是评估新型抗菌肽有效性的金标准，包括体外抗菌活性测试（MIC值测定）、细胞毒性测试、稳定性测试等。这超出了计算模型的范畴，但模型的目标是提高筛选出优质候选肽的效率。

## 6. 总结与未来展望

本项目提出了一套完整的多模态对比学习与条件生成相结合的框架，用于新型抗革兰氏阴性菌肽段的设计。通过精心设计的数据策略、模型架构、损失函数和训练流程，期望能够学习到序列与结构之间的深层联系，并生成具有高潜力的新候选肽。

**未来可探索的方向包括：**
*   更复杂的联合序列-结构扩散模型。
*   引入GAN进行生成结果的精炼或评估。
*   结合强化学习，根据特定的优化目标（如最大化预测活性，最小化毒性）进行目标导向的肽段生成。
*   迭代的实验-计算循环：用实验验证结果反馈和优化模型。

---

这份文档力求详尽，希望能为您后续的工作提供清晰的指引。