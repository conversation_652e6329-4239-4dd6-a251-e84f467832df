<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gram-Negative Bacteria Prediction System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-virus2"></i> Gram-Negative Bacteria Prediction System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/"><i class="bi bi-house-door"></i> Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about"><i class="bi bi-info-circle"></i> About</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <div class="container my-5">
        <div class="row">
            <!-- 左侧输入区 -->
            <div class="col-lg-6">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-input-cursor-text"></i> Sequence Input</h5>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="inputTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="file-tab" data-bs-toggle="tab" data-bs-target="#file-pane" type="button" role="tab">File Upload</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="text-tab" data-bs-toggle="tab" data-bs-target="#text-pane" type="button" role="tab">Text Input</button>
                            </li>
                        </ul>
                        
                        <div class="tab-content mt-3" id="inputTabContent">
                            <!-- 文件上传面板 -->
                            <div class="tab-pane fade show active" id="file-pane" role="tabpanel">
                                <form id="fileForm" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <div class="file-upload-container" id="dropZone">
                                            <div class="file-upload-content text-center p-5">
                                                <i class="bi bi-cloud-arrow-up display-4"></i>
                                                <h5 class="mt-3">Drag and drop FASTA file here</h5>
                                                <p class="text-muted">Or</p>
                                                <label for="fastaFile" class="btn btn-primary">
                                                    Choose File
                                                </label>
                                                <input type="file" id="fastaFile" name="fasta_file" accept=".fasta,.fa,.txt" class="d-none">
                                                <p class="selected-file-name mt-2 text-muted"></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="bi bi-lightning-charge"></i> Start Prediction
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- 文本输入面板 -->
                            <div class="tab-pane fade" id="text-pane" role="tabpanel">
                                <form id="textForm">
                                    <div class="mb-3">
                                        <label for="fastaText" class="form-label">FASTA Format Sequence</label>
                                        <textarea class="form-control" id="fastaText" name="fasta_text" rows="10" placeholder=">Sequence ID&#10;ACDEFGHIKLMNPQRSTVWY"></textarea>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" id="loadExample" class="btn btn-outline-primary">
                                            <i class="bi bi-clipboard-data"></i> Load Example
                                        </button>
                                        <button type="submit" class="btn btn-success">
                                            <i class="bi bi-lightning-charge"></i> Start Prediction
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 序列格式说明 -->
                <div class="card shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle"></i> Sequence Format Instructions</h5>
                    </div>
                    <div class="card-body">
                        <p>Please provide peptide sequences in FASTA format as follows:</p>
                        <pre class="bg-light p-3 rounded">
>SequenceID1
ACDEFGHIKLMNPQRSTVWY
>SequenceID2
GLWSKIKEVGKEAAKAAAKAAGKAALGAVSEAV</pre>
                        <p>Notes:</p>
                        <ul>
                            <li>Sequences only support 20 standard amino acid letters: A, C, D, E, F, G, H, I, K, L, M, N, P, Q, R, S, T, V, W, Y</li>
                            <li>Recommended length for each sequence is between 5-100 amino acids.</li>
                            <li>A single prediction supports up to 20,000 sequences.</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 右侧结果区 -->
            <div class="col-lg-6">
                <!-- 加载中提示 -->
                <div id="loadingSection" class="d-none">
                    <div class="card shadow-sm mb-4">
                        <div class="card-body text-center py-5">
                            <div class="spinner-border text-primary" role="status"></div>
                            <h5 class="mt-3">Processing sequence data...</h5>
                            <p class="text-muted">Please wait, this may take a few seconds.</p>
                        </div>
                    </div>
                </div>
                
                <!-- 结果区域 -->
                <div id="resultsSection" class="d-none">
                    <!-- 结果统计卡片 -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="bi bi-bar-chart"></i> Prediction Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <h3 id="totalSequences">0</h3>
                                    <p>Total Sequences</p>
                                </div>
                                <div class="col-4">
                                    <h3 id="positiveCount">0</h3>
                                    <p>Predicted Positive</p>
                                </div>
                                <div class="col-4">
                                    <h3 id="positivePercentage">0%</h3>
                                    <p>Positive Percentage</p>
                                </div>
                            </div>
                            
                            <!-- 图表容器 -->
                            <div class="mt-4">
                                <canvas id="resultChart" height="200"></canvas>
                            </div>
                            
                            <!-- 导出按钮 -->
                            <div class="d-flex justify-content-end mt-3">
                                <button id="exportCSV" class="btn btn-outline-primary me-2">
                                    <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
                                </button>
                                <button id="exportFASTA" class="btn btn-outline-success">
                                    <i class="bi bi-file-earmark-text"></i> Export Positive FASTA
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 详细结果表格 -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-table"></i> Detailed Prediction Results</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <table class="table table-striped table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Sequence ID</th>
                                            <th>Sequence</th>
                                            <th>Prediction Probability</th>
                                            <th>Prediction Result</th>
                                        </tr>
                                    </thead>
                                    <tbody id="resultsTableBody">
                                        <!-- Results will be dynamically added via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 错误提示 -->
                <div id="errorSection" class="d-none">
                    <div class="card shadow-sm mb-4 border-danger">
                        <div class="card-body text-center py-4">
                            <i class="bi bi-exclamation-triangle text-danger display-4"></i>
                            <h5 class="mt-3 text-danger">Processing Error</h5>
                            <p id="errorMessage" class="text-muted"></p>
                            <button id="resetForms" class="btn btn-outline-primary mt-2">
                                <i class="bi bi-arrow-counterclockwise"></i> Start Over
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Gram-Negative Bacteria Prediction System</h5>
                    <p class="text-muted">Peptide sequence classification model based on hyperbolic space embedding.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">© 2025 AIBD Research Team</p>
                    <p class="text-muted">Based on Hyperbolic Space Machine Learning Model</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript 依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
